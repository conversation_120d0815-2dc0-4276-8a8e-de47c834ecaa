// Product Types - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 8: TypeScript interface kullanımı
// ✅ Kural 2: PascalCase dosya isimlendirme

import { ApiResponse, PaginatedResponse } from './index'

// ✅ Kural 8: Union types kullanımı
export type ProductUnit = 
  | 'PIECE' 
  | 'KG' 
  | 'GRAM' 
  | 'LITER' 
  | 'ML' 
  | 'PORTION' 
  | 'BOX' 
  | 'PACKAGE'

// ✅ Kural 8: Interface tanımlama (genişletilebilir)
export interface Product {
  id: string
  companyId: string
  categoryId: string
  
  code: string // SKU
  barcode?: string
  name: string
  description?: string
  shortDescription?: string
  image?: string
  images: string[]
  
  basePrice: number
  taxId: string
  
  // Maliyet takibi
  costPrice?: number
  profitMargin?: number
  
  trackStock: boolean
  unit: ProductUnit
  criticalStock?: number
  
  // Satış özellikleri
  available: boolean
  sellable: boolean
  preparationTime?: number // dakika
  calories?: number
  allergens: string[]
  
  hasVariants: boolean
  hasModifiers: boolean
  
  // QR menü özellikleri
  showInMenu: boolean
  featured: boolean
  
  displayOrder: number
  active: boolean
  version: number
  createdAt: string
  updatedAt: string
  deletedAt?: string
  
  syncId?: string
  lastSyncAt?: string
  
  // Relations
  category?: Category
  tax?: Tax
  variants?: ProductVariant[]
  modifierGroups?: ProductModifierGroup[]
}

export interface Category {
  id: string
  companyId: string
  parentId?: string
  
  name: string
  description?: string
  image?: string
  color?: string
  icon?: string
  
  displayOrder: number
  active: boolean
  createdAt: string
  updatedAt: string
  deletedAt?: string
  
  // Relations
  products?: Product[]
  children?: Category[]
  parent?: Category
}

export interface Tax {
  id: string
  companyId: string
  
  name: string
  rate: number
  type: 'PERCENTAGE' | 'FIXED'
  active: boolean
  createdAt: string
  updatedAt: string
}

export interface ProductVariant {
  id: string
  productId: string
  
  name: string // e.g. "Small", "Large"
  code: string // e.g. "S", "L"
  price: number
  costPrice?: number
  
  available: boolean
  displayOrder: number
  active: boolean
  createdAt: string
  updatedAt: string
  deletedAt?: string
}

export interface ProductModifierGroup {
  productId: string
  modifierGroupId: string
  displayOrder: number
  
  product: Product
  modifierGroup: ModifierGroup
}

export interface ModifierGroup {
  id: string
  companyId: string
  
  name: string
  description?: string
  minSelection: number
  maxSelection: number
  required: boolean
  
  displayOrder: number
  active: boolean
  createdAt: string
  updatedAt: string
  deletedAt?: string
  
  // Relations
  modifiers?: Modifier[]
}

export interface Modifier {
  id: string
  groupId: string
  
  name: string
  description?: string
  price: number
  
  available: boolean
  displayOrder: number
  active: boolean
  createdAt: string
  updatedAt: string
  deletedAt?: string
}

// ✅ Kural 7: API Request/Response Types
export interface CreateProductRequest {
  categoryId: string
  code: string
  barcode?: string
  name: string
  description?: string
  shortDescription?: string
  image?: string
  images?: string[]
  basePrice: number
  taxId: string
  costPrice?: number
  profitMargin?: number
  trackStock?: boolean
  unit?: ProductUnit
  criticalStock?: number
  available?: boolean
  sellable?: boolean
  preparationTime?: number
  calories?: number
  allergens?: string[]
  hasVariants?: boolean
  hasModifiers?: boolean
  showInMenu?: boolean
  featured?: boolean
  displayOrder?: number
}

export interface UpdateProductRequest {
  categoryId?: string
  code?: string
  barcode?: string
  name?: string
  description?: string
  shortDescription?: string
  image?: string
  images?: string[]
  basePrice?: number
  taxId?: string
  costPrice?: number
  profitMargin?: number
  trackStock?: boolean
  unit?: ProductUnit
  criticalStock?: number
  available?: boolean
  sellable?: boolean
  preparationTime?: number
  calories?: number
  allergens?: string[]
  hasVariants?: boolean
  hasModifiers?: boolean
  showInMenu?: boolean
  featured?: boolean
  displayOrder?: number
  active?: boolean
}

export interface ProductQueryParams {
  page?: number
  limit?: number
  search?: string
  categoryId?: string
  available?: boolean
  sellable?: boolean
  featured?: boolean
  hasVariants?: boolean
  hasModifiers?: boolean
  sortBy?: 'name' | 'basePrice' | 'displayOrder' | 'createdAt' | 'updatedAt'
  sortOrder?: 'asc' | 'desc'
}

// ✅ Kural 7: Response formatları
export type ProductResponse = ApiResponse<Product>
export type ProductListResponse = PaginatedResponse<Product>
export type CategoryResponse = ApiResponse<Category>
export type CategoryListResponse = PaginatedResponse<Category>
export type TaxResponse = ApiResponse<Tax>
export type TaxListResponse = PaginatedResponse<Tax>

// ✅ Kural 8: Export pattern
export type {
  Product as ProductType,
  Category as CategoryType,
  Tax as TaxType,
  ProductVariant as ProductVariantType,
  Modifier as ModifierType,
  ModifierGroup as ModifierGroupType
}
