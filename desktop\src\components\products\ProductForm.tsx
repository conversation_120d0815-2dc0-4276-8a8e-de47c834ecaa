// Product Form Component - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 14: Component Structure Pattern
// ✅ Kural 4: i18n kullanımı
// ✅ Kural 19: Tema sistemi kullanımı

import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Grid,
  Box,
  Typography,
  CircularProgress,
  Alert,
  InputAdornment,
  Divider,
  useTheme,
  useMediaQuery
} from '@mui/material'
import {
  Save as SaveIcon,
  Close as CloseIcon
} from '@mui/icons-material'
import { useProductStore } from '../../store/useProductStore'
import { Product, CreateProductRequest, UpdateProductRequest, ProductUnit } from '@shared/types/ProductTypes'
import { CURRENCY } from '@shared/constants'

// ✅ Kural 14: Component props interface
interface ProductFormProps {
  open: boolean
  product?: Product | null
  onClose: () => void
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

// ✅ Form data interface
interface FormData {
  name: string
  code: string
  barcode: string
  description: string
  shortDescription: string
  image: string
  basePrice: string
  costPrice: string
  profitMargin: string
  categoryId: string
  taxId: string
  unit: ProductUnit
  criticalStock: string
  preparationTime: string
  calories: string
  allergens: string
  trackStock: boolean
  available: boolean
  sellable: boolean
  showInMenu: boolean
  featured: boolean
}

// ✅ Validation errors interface
interface ValidationErrors {
  [key: string]: string
}

// ✅ Default form data
const defaultFormData: FormData = {
  name: '',
  code: '',
  barcode: '',
  description: '',
  shortDescription: '',
  image: '',
  basePrice: '0.00',
  costPrice: '0.00',
  profitMargin: '0',
  categoryId: '',
  taxId: '',
  unit: 'PIECE',
  criticalStock: '10',
  preparationTime: '5',
  calories: '0',
  allergens: '',
  trackStock: false,
  available: true,
  sellable: true,
  showInMenu: true,
  featured: false
}

// ✅ Kural 14: Standard component structure
export const ProductForm: React.FC<ProductFormProps> = ({
  open,
  product,
  onClose,
  onSuccess,
  onError
}) => {
  // ========== 1. HOOKS ==========
  // ✅ i18n hook
  const { t } = useTranslation()
  
  // ✅ Theme hook
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  
  // ✅ Store hooks
  const {
    categories,
    taxes,
    loading,
    createProduct,
    updateProduct
  } = useProductStore()
  
  // ✅ Local state hooks
  const [formData, setFormData] = useState<FormData>(defaultFormData)
  const [errors, setErrors] = useState<ValidationErrors>({})
  const [submitting, setSubmitting] = useState(false)
  
  // ========== 2. COMPUTED VALUES ==========
  
  /**
   * ✅ Kural 17: useMemo for computed values
   */
  const isEditing = useMemo(() => !!product, [product])
  
  const dialogTitle = useMemo(() => {
    return isEditing ? t('products.form.edit') : t('products.form.add')
  }, [isEditing, t])
  
  const submitButtonText = useMemo(() => {
    if (submitting) {
      return t('products.form.saving')
    }
    return isEditing ? t('products.form.update') : t('products.form.create')
  }, [submitting, isEditing, t])
  
  // ========== 3. VALIDATION ==========
  
  /**
   * ✅ Validate form data
   */
  const validateForm = useCallback((): boolean => {
    const newErrors: ValidationErrors = {}
    
    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = t('products.form.validation.nameRequired')
    } else if (formData.name.length < 2) {
      newErrors.name = t('products.form.validation.nameMinLength')
    } else if (formData.name.length > 200) {
      newErrors.name = t('products.form.validation.nameMaxLength')
    }
    
    // Code validation
    if (!formData.code.trim()) {
      newErrors.code = t('products.form.validation.codeRequired')
    } else if (!/^[A-Z0-9_-]+$/.test(formData.code)) {
      newErrors.code = t('products.form.validation.codeFormat')
    } else if (formData.code.length > 50) {
      newErrors.code = t('products.form.validation.codeMaxLength')
    }
    
    // Barcode validation
    if (formData.barcode && formData.barcode.length > 50) {
      newErrors.barcode = t('products.form.validation.barcodeMaxLength')
    }
    
    // Price validation
    const basePrice = parseFloat(formData.basePrice)
    if (isNaN(basePrice) || basePrice < 0) {
      newErrors.basePrice = t('products.form.validation.priceMin')
    } else if (basePrice > 999999.99) {
      newErrors.basePrice = t('products.form.validation.priceMax')
    }
    
    // Cost price validation
    if (formData.costPrice) {
      const costPrice = parseFloat(formData.costPrice)
      if (isNaN(costPrice) || costPrice < 0) {
        newErrors.costPrice = t('products.form.validation.costPriceMin')
      }
    }
    
    // Profit margin validation
    if (formData.profitMargin) {
      const profitMargin = parseFloat(formData.profitMargin)
      if (isNaN(profitMargin) || profitMargin < 0 || profitMargin > 100) {
        newErrors.profitMargin = t('products.form.validation.profitMarginRange')
      }
    }
    
    // Category validation
    if (!formData.categoryId) {
      newErrors.categoryId = t('products.form.validation.categoryRequired')
    }
    
    // Tax validation
    if (!formData.taxId) {
      newErrors.taxId = t('products.form.validation.taxRequired')
    }
    
    // Description validation
    if (formData.description && formData.description.length > 1000) {
      newErrors.description = t('products.form.validation.descriptionMaxLength')
    }
    
    // Short description validation
    if (formData.shortDescription && formData.shortDescription.length > 200) {
      newErrors.shortDescription = t('products.form.validation.shortDescriptionMaxLength')
    }
    
    // Image URL validation
    if (formData.image && formData.image.trim()) {
      try {
        new URL(formData.image)
      } catch {
        newErrors.image = t('products.form.validation.imageUrl')
      }
    }
    
    // Critical stock validation
    if (formData.criticalStock) {
      const criticalStock = parseFloat(formData.criticalStock)
      if (isNaN(criticalStock) || criticalStock < 0) {
        newErrors.criticalStock = t('products.form.validation.criticalStockMin')
      }
    }
    
    // Preparation time validation
    if (formData.preparationTime) {
      const preparationTime = parseFloat(formData.preparationTime)
      if (isNaN(preparationTime) || preparationTime < 0) {
        newErrors.preparationTime = t('products.form.validation.preparationTimeMin')
      }
    }
    
    // Calories validation
    if (formData.calories) {
      const calories = parseFloat(formData.calories)
      if (isNaN(calories) || calories < 0) {
        newErrors.calories = t('products.form.validation.caloriesMin')
      }
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [formData, t])
  
  // ========== 4. EVENT HANDLERS ==========
  
  /**
   * ✅ Handle input change
   */
  const handleInputChange = useCallback((field: keyof FormData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const value = event.target.value
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }, [errors])
  
  /**
   * ✅ Handle select change
   */
  const handleSelectChange = useCallback((field: keyof FormData) => (
    event: any
  ) => {
    const value = event.target.value
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }, [errors])
  
  /**
   * ✅ Handle switch change
   */
  const handleSwitchChange = useCallback((field: keyof FormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.checked
    setFormData(prev => ({ ...prev, [field]: value }))
  }, [])
  
  /**
   * ✅ Handle form submit
   */
  const handleSubmit = useCallback(async (event: React.FormEvent) => {
    event.preventDefault()
    
    if (!validateForm()) {
      return
    }
    
    setSubmitting(true)
    
    try {
      // Prepare data for API
      const apiData: CreateProductRequest | UpdateProductRequest = {
        name: formData.name.trim(),
        code: formData.code.trim().toUpperCase(),
        barcode: formData.barcode.trim() || undefined,
        description: formData.description.trim() || undefined,
        shortDescription: formData.shortDescription.trim() || undefined,
        image: formData.image.trim() || undefined,
        basePrice: parseFloat(formData.basePrice),
        costPrice: formData.costPrice ? parseFloat(formData.costPrice) : undefined,
        profitMargin: formData.profitMargin ? parseFloat(formData.profitMargin) : undefined,
        categoryId: formData.categoryId,
        taxId: formData.taxId,
        unit: formData.unit,
        criticalStock: formData.criticalStock ? parseFloat(formData.criticalStock) : undefined,
        preparationTime: formData.preparationTime ? parseFloat(formData.preparationTime) : undefined,
        calories: formData.calories ? parseFloat(formData.calories) : undefined,
        allergens: formData.allergens ? formData.allergens.split(',').map(a => a.trim()).filter(Boolean) : [],
        trackStock: formData.trackStock,
        available: formData.available,
        sellable: formData.sellable,
        showInMenu: formData.showInMenu,
        featured: formData.featured
      }
      
      if (isEditing && product) {
        await updateProduct(product.id, apiData)
        onSuccess(t('products.messages.updateSuccess') || 'Ürün başarıyla güncellendi')
      } else {
        await createProduct(apiData)
        onSuccess(t('products.messages.createSuccess') || 'Ürün başarıyla oluşturuldu')
      }
      
      onClose()
    } catch (error: any) {
      onError(error.message || t('products.messages.createError') || 'Ürün kaydedilirken hata oluştu')
    } finally {
      setSubmitting(false)
    }
  }, [formData, validateForm, isEditing, product, updateProduct, createProduct, onSuccess, onError, onClose, t])
  
  /**
   * ✅ Handle close
   */
  const handleClose = useCallback(() => {
    if (!submitting) {
      onClose()
    }
  }, [submitting, onClose])
  
  // ========== 5. EFFECTS ==========
  
  /**
   * ✅ Initialize form data when product changes
   */
  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name || '',
        code: product.code || '',
        barcode: product.barcode || '',
        description: product.description || '',
        shortDescription: product.shortDescription || '',
        image: product.image || '',
        basePrice: product.basePrice?.toString() || '0.00',
        costPrice: product.costPrice?.toString() || '',
        profitMargin: product.profitMargin?.toString() || '',
        categoryId: product.categoryId || '',
        taxId: product.taxId || '',
        unit: product.unit || 'PIECE',
        criticalStock: product.criticalStock?.toString() || '',
        preparationTime: product.preparationTime?.toString() || '',
        calories: product.calories?.toString() || '',
        allergens: product.allergens?.join(', ') || '',
        trackStock: product.trackStock || false,
        available: product.available !== false,
        sellable: product.sellable !== false,
        showInMenu: product.showInMenu !== false,
        featured: product.featured || false
      })
    } else {
      setFormData(defaultFormData)
    }
    
    setErrors({})
  }, [product])
  
  // ========== 6. RENDER HELPERS ==========
  
  /**
   * ✅ Render basic information section
   */
  const renderBasicInfo = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('products.form.basicInfo')}
      </Typography>

      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label={t('products.name')}
            value={formData.name}
            onChange={handleInputChange('name')}
            error={!!errors.name}
            helperText={errors.name}
            placeholder={t('products.form.placeholders.name')}
            required
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label={t('products.code')}
            value={formData.code}
            onChange={handleInputChange('code')}
            error={!!errors.code}
            helperText={errors.code}
            placeholder={t('products.form.placeholders.code')}
            required
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label={t('products.barcode')}
            value={formData.barcode}
            onChange={handleInputChange('barcode')}
            error={!!errors.barcode}
            helperText={errors.barcode}
            placeholder={t('products.form.placeholders.barcode')}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth error={!!errors.unit}>
            <InputLabel>{t('products.unit')}</InputLabel>
            <Select
              value={formData.unit}
              onChange={handleSelectChange('unit')}
              label={t('products.unit')}
            >
              <MenuItem value="PIECE">{t('products.units.PIECE')}</MenuItem>
              <MenuItem value="KG">{t('products.units.KG')}</MenuItem>
              <MenuItem value="GRAM">{t('products.units.GRAM')}</MenuItem>
              <MenuItem value="LITER">{t('products.units.LITER')}</MenuItem>
              <MenuItem value="ML">{t('products.units.ML')}</MenuItem>
              <MenuItem value="PORTION">{t('products.units.PORTION')}</MenuItem>
              <MenuItem value="BOX">{t('products.units.BOX')}</MenuItem>
              <MenuItem value="PACKAGE">{t('products.units.PACKAGE')}</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            label={t('products.shortDescription')}
            value={formData.shortDescription}
            onChange={handleInputChange('shortDescription')}
            error={!!errors.shortDescription}
            helperText={errors.shortDescription}
            placeholder={t('products.form.placeholders.shortDescription')}
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            multiline
            rows={3}
            label={t('products.description')}
            value={formData.description}
            onChange={handleInputChange('description')}
            error={!!errors.description}
            helperText={errors.description}
            placeholder={t('products.form.placeholders.description')}
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            label={t('products.image')}
            value={formData.image}
            onChange={handleInputChange('image')}
            error={!!errors.image}
            helperText={errors.image}
            placeholder={t('products.form.placeholders.imageUrl')}
          />
        </Grid>
      </Grid>
    </Box>
  )

  /**
   * ✅ Render pricing section
   */
  const renderPricing = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('products.form.pricing')}
      </Typography>

      <Grid container spacing={2}>
        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            type="number"
            label={t('products.basePrice')}
            value={formData.basePrice}
            onChange={handleInputChange('basePrice')}
            error={!!errors.basePrice}
            helperText={errors.basePrice}
            placeholder={t('products.form.placeholders.basePrice')}
            required
            InputProps={{
              endAdornment: <InputAdornment position="end">{CURRENCY.SYMBOL}</InputAdornment>
            }}
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            type="number"
            label={t('products.costPrice')}
            value={formData.costPrice}
            onChange={handleInputChange('costPrice')}
            error={!!errors.costPrice}
            helperText={errors.costPrice}
            placeholder={t('products.form.placeholders.costPrice')}
            InputProps={{
              endAdornment: <InputAdornment position="end">{CURRENCY.SYMBOL}</InputAdornment>
            }}
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            type="number"
            label={t('products.profitMargin')}
            value={formData.profitMargin}
            onChange={handleInputChange('profitMargin')}
            error={!!errors.profitMargin}
            helperText={errors.profitMargin}
            placeholder={t('products.form.placeholders.profitMargin')}
            InputProps={{
              endAdornment: <InputAdornment position="end">%</InputAdornment>
            }}
          />
        </Grid>
      </Grid>
    </Box>
  )

  /**
   * ✅ Render category and tax section
   */
  const renderCategoryTax = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('products.form.categoryTax')}
      </Typography>

      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth error={!!errors.categoryId} required>
            <InputLabel>{t('products.category')}</InputLabel>
            <Select
              value={formData.categoryId}
              onChange={handleSelectChange('categoryId')}
              label={t('products.category')}
            >
              {categories.map((category) => (
                <MenuItem key={category.id} value={category.id}>
                  {category.name}
                </MenuItem>
              ))}
            </Select>
            {errors.categoryId && (
              <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                {errors.categoryId}
              </Typography>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth error={!!errors.taxId} required>
            <InputLabel>{t('products.tax')}</InputLabel>
            <Select
              value={formData.taxId}
              onChange={handleSelectChange('taxId')}
              label={t('products.tax')}
            >
              {taxes.map((tax) => (
                <MenuItem key={tax.id} value={tax.id}>
                  {tax.name} ({tax.rate}%)
                </MenuItem>
              ))}
            </Select>
            {errors.taxId && (
              <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                {errors.taxId}
              </Typography>
            )}
          </FormControl>
        </Grid>
      </Grid>
    </Box>
  )

  /**
   * ✅ Render additional info section
   */
  const renderAdditionalInfo = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('products.form.additionalInfo')}
      </Typography>

      <Grid container spacing={2}>
        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            type="number"
            label={t('products.preparationTime')}
            value={formData.preparationTime}
            onChange={handleInputChange('preparationTime')}
            error={!!errors.preparationTime}
            helperText={errors.preparationTime}
            placeholder={t('products.form.placeholders.preparationTime')}
            InputProps={{
              endAdornment: <InputAdornment position="end">dk</InputAdornment>
            }}
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            type="number"
            label={t('products.calories')}
            value={formData.calories}
            onChange={handleInputChange('calories')}
            error={!!errors.calories}
            helperText={errors.calories}
            placeholder={t('products.form.placeholders.calories')}
            InputProps={{
              endAdornment: <InputAdornment position="end">kcal</InputAdornment>
            }}
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            type="number"
            label={t('products.criticalStock')}
            value={formData.criticalStock}
            onChange={handleInputChange('criticalStock')}
            error={!!errors.criticalStock}
            helperText={errors.criticalStock}
            placeholder={t('products.form.placeholders.criticalStock')}
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            label={t('products.allergens')}
            value={formData.allergens}
            onChange={handleInputChange('allergens')}
            error={!!errors.allergens}
            helperText={errors.allergens || 'Virgülle ayırarak yazın (örn: Gluten, Süt)'}
            placeholder={t('products.form.placeholders.allergens')}
          />
        </Grid>
      </Grid>
    </Box>
  )

  /**
   * ✅ Render settings section
   */
  const renderSettings = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('products.form.settings')}
      </Typography>

      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.trackStock}
                onChange={handleSwitchChange('trackStock')}
                color="primary"
              />
            }
            label={t('products.trackStock')}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.available}
                onChange={handleSwitchChange('available')}
                color="primary"
              />
            }
            label={t('products.available')}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.sellable}
                onChange={handleSwitchChange('sellable')}
                color="primary"
              />
            }
            label={t('products.sellable')}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.showInMenu}
                onChange={handleSwitchChange('showInMenu')}
                color="primary"
              />
            }
            label={t('products.showInMenu')}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.featured}
                onChange={handleSwitchChange('featured')}
                color="primary"
              />
            }
            label={t('products.featured')}
          />
        </Grid>
      </Grid>
    </Box>
  )
  
  // ========== 7. MAIN RENDER ==========
  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      fullScreen={isMobile}
    >
      <form onSubmit={handleSubmit}>
        <DialogTitle>
          {dialogTitle}
        </DialogTitle>
        
        <DialogContent dividers>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* Basic Information */}
            {renderBasicInfo()}

            <Divider />

            {/* Pricing */}
            {renderPricing()}

            <Divider />

            {/* Category and Tax */}
            {renderCategoryTax()}

            <Divider />

            {/* Additional Information */}
            {renderAdditionalInfo()}

            <Divider />

            {/* Settings */}
            {renderSettings()}
          </Box>
        </DialogContent>
        
        <DialogActions>
          <Button
            onClick={handleClose}
            disabled={submitting}
          >
            {t('common.cancel')}
          </Button>
          
          <Button
            type="submit"
            variant="contained"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={16} /> : <SaveIcon />}
          >
            {submitButtonText}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  )
}
